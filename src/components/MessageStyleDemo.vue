<template>
  <div class="message-demo-container">
    <div class="demo-header">
      <h2>ElMessage 样式演示</h2>
      <p>测试不同类型的消息提示样式</p>
    </div>
    
    <div class="demo-buttons">
      <el-button type="success" @click="showSuccessMessage">
        <el-icon><Check /></el-icon>
        成功消息
      </el-button>
      
      <el-button type="danger" @click="showErrorMessage">
        <el-icon><Close /></el-icon>
        错误消息
      </el-button>
      
      <el-button type="warning" @click="showWarningMessage">
        <el-icon><Warning /></el-icon>
        警告消息
      </el-button>
      
      <el-button type="info" @click="showInfoMessage">
        <el-icon><InfoFilled /></el-icon>
        信息消息
      </el-button>
      
      <el-button type="primary" @click="showLongMessage">
        <el-icon><Document /></el-icon>
        长文本消息
      </el-button>
      
      <el-button @click="showCloseableMessage">
        <el-icon><Setting /></el-icon>
        可关闭消息
      </el-button>
    </div>
    
    <div class="demo-description">
      <h3>样式特点</h3>
      <ul>
        <li>柔和的背景色彩，提升视觉舒适度</li>
        <li>优雅的毛玻璃效果和微妙阴影</li>
        <li>圆角设计 (8px)，与项目风格统一</li>
        <li>平滑的进入和退出动画</li>
        <li>完整的暗色模式适配</li>
        <li>响应式布局，移动端友好</li>
        <li>清晰的层次结构和可读性</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { Check, Close, Warning, InfoFilled, Document, Setting } from '@element-plus/icons-vue'

// 成功消息
const showSuccessMessage = () => {
  ElMessage.success({
    message: '操作成功！数据已保存',
    duration: 3000,
    showClose: false
  })
}

// 错误消息
const showErrorMessage = () => {
  ElMessage.error({
    message: '操作失败，请检查网络连接后重试',
    duration: 4000,
    showClose: false
  })
}

// 警告消息
const showWarningMessage = () => {
  ElMessage.warning({
    message: '请注意：此操作不可撤销',
    duration: 3000,
    showClose: false
  })
}

// 信息消息
const showInfoMessage = () => {
  ElMessage.info({
    message: '系统将在5分钟后进行维护',
    duration: 3000,
    showClose: false
  })
}

// 长文本消息
const showLongMessage = () => {
  ElMessage.success({
    message: '这是一个比较长的消息文本，用来测试消息框在显示长文本时的样式表现，包括文本换行、容器宽度适配等效果。新的样式采用了更柔和的色彩搭配。',
    duration: 5000,
    showClose: false
  })
}

// 可关闭消息
const showCloseableMessage = () => {
  ElMessage({
    message: '这是一个可以手动关闭的消息，点击右侧 × 按钮可关闭',
    type: 'info',
    duration: 0, // 不自动关闭
    showClose: true
  })
}
</script>

<style scoped>
.message-demo-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

body.dark .message-demo-container {
  background-color: var(--bg-card);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
}

.demo-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

body.dark .demo-header h2 {
  background: linear-gradient(135deg, #818cf8 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.demo-header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

body.dark .demo-header p {
  color: var(--text-secondary);
}

.demo-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 40px;
}

.demo-buttons .el-button {
  height: 48px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.demo-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.demo-description {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 24px;
  border-left: 4px solid #6366f1;
}

body.dark .demo-description {
  background-color: var(--bg-tertiary);
  border-left-color: #818cf8;
}

.demo-description h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

body.dark .demo-description h3 {
  color: var(--text-primary);
}

.demo-description ul {
  margin: 0;
  padding-left: 20px;
}

.demo-description li {
  font-size: 14px;
  color: #4b5563;
  margin-bottom: 8px;
  line-height: 1.5;
}

body.dark .demo-description li {
  color: var(--text-secondary);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .message-demo-container {
    margin: 20px;
    padding: 24px 16px;
  }
  
  .demo-header h2 {
    font-size: 24px;
  }
  
  .demo-buttons {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .demo-buttons .el-button {
    height: 44px;
  }
  
  .demo-description {
    padding: 20px;
  }
}
</style>
