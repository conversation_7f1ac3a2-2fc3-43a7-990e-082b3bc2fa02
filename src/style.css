@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* 防止移动设备上的缩放问题 */
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* 全局自定义滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

/* 暗色模式下的滚动条样式 */
body.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark ::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

.el-textarea__inner {
  border-radius: 8px !important;
  resize: none !important;
  box-shadow: 0 0 0 0 !important;
  background-color: transparent !important;
}

body.dark .el-input__wrapper {
  box-shadow: 0 0 0 0 !important;
}

/* 输入框滚动条样式 */
.el-textarea__inner::-webkit-scrollbar {
  width: 4px;
}

.el-textarea__inner::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-textarea__inner::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .el-textarea__inner {
  box-shadow: 0 0 0 0 !important;
}

body.dark .el-textarea__inner::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark .el-textarea__inner::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

.el-tooltip__trigger:focus-visible {
  outline: unset;
}

/* Element UI 输入框样式 */
.el-input__inner::-webkit-scrollbar {
  width: 4px;
}

.el-input__inner::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-input__inner::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .el-input__inner::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark .el-input__inner::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

/* 下拉菜单滚动条样式 */
.el-select-dropdown__wrap::-webkit-scrollbar {
  width: 4px;
}

.el-select-dropdown__wrap::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-select-dropdown__wrap::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .el-select-dropdown__wrap::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark .el-select-dropdown__wrap::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

/* 移动设备输入控件样式 */
@media (max-width: 768px) {
  input,
  textarea,
  select,
  .el-input__inner,
  .el-textarea__inner {
    font-size: 16px !important; /* 防止iOS自动缩放 */
    transform: scale(1);
    touch-action: manipulation;
    -webkit-appearance: none;
    appearance: none;
  }
  
  body {
    touch-action: manipulation; /* 防止双击缩放 */
  }
  
  /* 防止按钮和输入框的聚焦缩放 */
  button, 
  [type="button"], 
  [type="reset"], 
  [type="submit"] {
    -webkit-appearance: none;
    appearance: none;
  }
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  /* max-width: 1280px; */
  margin: 0 auto;
  /* padding: 2rem; */
  text-align: center;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Ant Design X Vue 组件样式覆盖 */
:where(.css-dev-only-do-not-override-1r7p0rg).ant-bubble .ant-bubble-content,
.ant-bubble .ant-bubble-content {
  text-align: left !important;
}

.ant-bubble .ant-bubble-content-outlined{
  border: 0px !important;
  padding: 16px !important;
  background-color: #e9edfa !important;
}

.ax-bubble-content {
  white-space: pre-line !important;
  text-align: left !important;
}

.el-textarea .el-input__count{
  background-color: transparent !important;
}

.ant-bubble-footer {
  width: 100% !important;
  margin-top: 0px !important;
}

/* 打字机效果光标样式 */
.ax-typing-cursor {
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: #409eff;
  animation: cursor-blink 1s step-end infinite;
  vertical-align: text-bottom;
  margin-left: 2px;
}

/* 提高对话框的z-index，确保在Header组件上方显示 */
:deep(el-dialog) {
  z-index: 3000 !important;
}

:deep(el-overlay) {
  z-index: 2999 !important;
}

@keyframes cursor-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* 修复计算高度问题 */
[style*="max-height: calc(-350px + 100vh)"],
[style*="max-height:calc(-350px + 100vh)"] {
  max-height: none !important;
  height: 60vh !important;
}

/* 聊天气泡样式全局覆盖 */
.ax-bubble-list,
.ant-bubble-list,
.chat-bubble-list {
  min-height: 400px !important;
  height: 60vh !important;
  max-height: 60vh !important;
  overflow-y: auto !important;
}

/* Element UI Slider 滑块样式 - 让滑块变小 */
.el-slider__button {
  /* width: 14px !important;
  height: 14px !important;
  top: 0 !important;
  border: 2px solid #409eff !important; */
}

.el-slider__button:hover {
  transform: scale(1.1) !important;
}

.el-slider__button-wrapper {
  /* width: 14px !important;
  height: 14px !important;
  top: 0 !important;
  transform: translate(-5px, -50%) !important; */
}

.el-slider__button-wrapper:hover {
  /* cursor: grab !important;
  transform: translate(-5px, -50%) scale(1.1) !important; */
}

.el-slider__button-wrapper:active {
  /* cursor: grabbing !important;
  transform: translate(-5px, -50%) !important; */
}

.el-slider__runway{
  margin: 0 !important;
}

.el-slider{
  height: 20px;
}

body.dark .el-pager .more,
body.dark .el-pager .number,
body.dark .el-pagination .btn-prev,
body.dark .el-pagination .btn-next{
  color: #fff;
  background-color: #303133;
}

/* ElMessage 全局样式定制 - 与项目风格保持一致 */
.el-message {
  min-width: 320px;
  max-width: 500px;
  border-radius: 12px !important;
  border: none !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  backdrop-filter: blur(16px);
  font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, sans-serif;
  font-size: 14px;
  font-weight: 500;
  padding: 16px 20px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 3001 !important;
}

/* 成功消息样式 */
.el-message--success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.95) 0%, rgba(5, 150, 105, 0.95) 100%) !important;
  color: #ffffff !important;
  border-left: 4px solid #059669 !important;
}

.el-message--success .el-message__icon {
  color: #ffffff !important;
  font-size: 18px;
}

/* 错误消息样式 */
.el-message--error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 0.95) 100%) !important;
  color: #ffffff !important;
  border-left: 4px solid #dc2626 !important;
}

.el-message--error .el-message__icon {
  color: #ffffff !important;
  font-size: 18px;
}

/* 警告消息样式 */
.el-message--warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.95) 0%, rgba(217, 119, 6, 0.95) 100%) !important;
  color: #ffffff !important;
  border-left: 4px solid #d97706 !important;
}

.el-message--warning .el-message__icon {
  color: #ffffff !important;
  font-size: 18px;
}

/* 信息消息样式 */
.el-message--info {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.95) 0%, rgba(79, 70, 229, 0.95) 100%) !important;
  color: #ffffff !important;
  border-left: 4px solid #4f46e5 !important;
}

.el-message--info .el-message__icon {
  color: #ffffff !important;
  font-size: 18px;
}

/* 消息图标样式 */
.el-message__icon {
  margin-right: 12px !important;
  font-size: 18px;
  display: flex;
  align-items: center;
}

/* 消息内容样式 */
.el-message__content {
  font-size: 14px;
  line-height: 1.5;
  font-weight: 500;
  letter-spacing: 0.02em;
}

/* 关闭按钮样式 */
.el-message__closeBtn {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 16px !important;
  font-weight: bold !important;
  transition: all 0.2s ease;
  padding: 2px !important;
  border-radius: 4px;
}

.el-message__closeBtn:hover {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

/* 暗色模式下的 ElMessage 样式 */
body.dark .el-message {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* 暗色模式下的成功消息 */
body.dark .el-message--success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.9) 0%, rgba(5, 150, 105, 0.9) 100%) !important;
  border-left-color: #10b981 !important;
}

/* 暗色模式下的错误消息 */
body.dark .el-message--error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%) !important;
  border-left-color: #ef4444 !important;
}

/* 暗色模式下的警告消息 */
body.dark .el-message--warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.9) 0%, rgba(217, 119, 6, 0.9) 100%) !important;
  border-left-color: #f59e0b !important;
}

/* 暗色模式下的信息消息 */
body.dark .el-message--info {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(79, 70, 229, 0.9) 100%) !important;
  border-left-color: #6366f1 !important;
}

/* 消息动画效果 */
.el-message.el-message-fade-enter-active {
  animation: el-message-fade-in 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-message.el-message-fade-leave-active {
  animation: el-message-fade-out 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes el-message-fade-in {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes el-message-fade-out {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .el-message {
    min-width: 280px;
    max-width: calc(100vw - 32px);
    margin: 0 16px;
    font-size: 13px;
    padding: 14px 16px !important;
  }

  .el-message__icon {
    font-size: 16px;
    margin-right: 10px !important;
  }

  .el-message__content {
    font-size: 13px;
  }

  .el-message__closeBtn {
    font-size: 14px !important;
  }
}
