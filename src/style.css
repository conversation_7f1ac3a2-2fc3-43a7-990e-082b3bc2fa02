@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* 防止移动设备上的缩放问题 */
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* 全局自定义滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

/* 暗色模式下的滚动条样式 */
body.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark ::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

.el-textarea__inner {
  border-radius: 8px !important;
  resize: none !important;
  box-shadow: 0 0 0 0 !important;
  background-color: transparent !important;
}

body.dark .el-input__wrapper {
  box-shadow: 0 0 0 0 !important;
}

/* 输入框滚动条样式 */
.el-textarea__inner::-webkit-scrollbar {
  width: 4px;
}

.el-textarea__inner::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-textarea__inner::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .el-textarea__inner {
  box-shadow: 0 0 0 0 !important;
}

body.dark .el-textarea__inner::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark .el-textarea__inner::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

.el-tooltip__trigger:focus-visible {
  outline: unset;
}

/* Element UI 输入框样式 */
.el-input__inner::-webkit-scrollbar {
  width: 4px;
}

.el-input__inner::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-input__inner::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .el-input__inner::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark .el-input__inner::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

/* 下拉菜单滚动条样式 */
.el-select-dropdown__wrap::-webkit-scrollbar {
  width: 4px;
}

.el-select-dropdown__wrap::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-select-dropdown__wrap::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .el-select-dropdown__wrap::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark .el-select-dropdown__wrap::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

/* 移动设备输入控件样式 */
@media (max-width: 768px) {
  input,
  textarea,
  select,
  .el-input__inner,
  .el-textarea__inner {
    font-size: 16px !important; /* 防止iOS自动缩放 */
    transform: scale(1);
    touch-action: manipulation;
    -webkit-appearance: none;
    appearance: none;
  }
  
  body {
    touch-action: manipulation; /* 防止双击缩放 */
  }
  
  /* 防止按钮和输入框的聚焦缩放 */
  button, 
  [type="button"], 
  [type="reset"], 
  [type="submit"] {
    -webkit-appearance: none;
    appearance: none;
  }
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  /* max-width: 1280px; */
  margin: 0 auto;
  /* padding: 2rem; */
  text-align: center;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Ant Design X Vue 组件样式覆盖 */
:where(.css-dev-only-do-not-override-1r7p0rg).ant-bubble .ant-bubble-content,
.ant-bubble .ant-bubble-content {
  text-align: left !important;
}

.ant-bubble .ant-bubble-content-outlined{
  border: 0px !important;
  padding: 16px !important;
  background-color: #e9edfa !important;
}

.ax-bubble-content {
  white-space: pre-line !important;
  text-align: left !important;
}

.el-textarea .el-input__count{
  background-color: transparent !important;
}

.ant-bubble-footer {
  width: 100% !important;
  margin-top: 0px !important;
}

/* 打字机效果光标样式 */
.ax-typing-cursor {
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: #409eff;
  animation: cursor-blink 1s step-end infinite;
  vertical-align: text-bottom;
  margin-left: 2px;
}

/* 提高对话框的z-index，确保在Header组件上方显示 */
:deep(el-dialog) {
  z-index: 3000 !important;
}

:deep(el-overlay) {
  z-index: 2999 !important;
}

@keyframes cursor-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* 修复计算高度问题 */
[style*="max-height: calc(-350px + 100vh)"],
[style*="max-height:calc(-350px + 100vh)"] {
  max-height: none !important;
  height: 60vh !important;
}

/* 聊天气泡样式全局覆盖 */
.ax-bubble-list,
.ant-bubble-list,
.chat-bubble-list {
  min-height: 400px !important;
  height: 60vh !important;
  max-height: 60vh !important;
  overflow-y: auto !important;
}

/* Element UI Slider 滑块样式 - 让滑块变小 */
.el-slider__button {
  /* width: 14px !important;
  height: 14px !important;
  top: 0 !important;
  border: 2px solid #409eff !important; */
}

.el-slider__button:hover {
  transform: scale(1.1) !important;
}

.el-slider__button-wrapper {
  /* width: 14px !important;
  height: 14px !important;
  top: 0 !important;
  transform: translate(-5px, -50%) !important; */
}

.el-slider__button-wrapper:hover {
  /* cursor: grab !important;
  transform: translate(-5px, -50%) scale(1.1) !important; */
}

.el-slider__button-wrapper:active {
  /* cursor: grabbing !important;
  transform: translate(-5px, -50%) !important; */
}

.el-slider__runway{
  margin: 0 !important;
}

.el-slider{
  height: 20px;
}

body.dark .el-pager .more,
body.dark .el-pager .number,
body.dark .el-pagination .btn-prev,
body.dark .el-pagination .btn-next{
  color: #fff;
  background-color: #303133;
}

/* ElMessage 全局样式定制 - 与项目风格保持一致 */
.el-message {
  min-width: 320px;
  max-width: 500px;
  border-radius: 8px !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1), 0 3px 10px rgba(0, 0, 0, 0.06) !important;
  backdrop-filter: blur(12px);
  font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, sans-serif;
  font-size: 14px;
  font-weight: 400;
  padding: 14px 16px !important;
  transition: all 0.25s ease-out;
  z-index: 3001 !important;
  transform-origin: center bottom;
  will-change: transform, opacity, filter;

  /* 强制居中定位 */
  position: fixed !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* 成功消息样式 - 柔和的绿色 */
.el-message--success {
  background: rgba(240, 253, 244, 0.95) !important;
  color: #065f46 !important;
  border-left: 3px solid #10b981 !important;
  border-color: rgba(16, 185, 129, 0.2) !important;
}

.el-message--success .el-message__icon {
  color: #10b981 !important;
  font-size: 16px;
}

/* 错误消息样式 - 柔和的红色 */
.el-message--error {
  background: rgba(254, 242, 242, 0.95) !important;
  color: #7f1d1d !important;
  border-left: 3px solid #ef4444 !important;
  border-color: rgba(239, 68, 68, 0.2) !important;
}

.el-message--error .el-message__icon {
  color: #ef4444 !important;
  font-size: 16px;
}

/* 警告消息样式 - 柔和的橙色 */
.el-message--warning {
  background: rgba(255, 251, 235, 0.95) !important;
  color: #92400e !important;
  border-left: 3px solid #f59e0b !important;
  border-color: rgba(245, 158, 11, 0.2) !important;
}

.el-message--warning .el-message__icon {
  color: #f59e0b !important;
  font-size: 16px;
}

/* 信息消息样式 - 柔和的蓝色 */
.el-message--info {
  background: rgba(239, 246, 255, 0.95) !important;
  color: #1e3a8a !important;
  border-left: 3px solid #3b82f6 !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
}

.el-message--info .el-message__icon {
  color: #3b82f6 !important;
  font-size: 16px;
}

/* 消息图标样式 */
.el-message__icon {
  margin-right: 10px !important;
  font-size: 16px;
  display: flex;
  align-items: center;
  opacity: 0.9;
}

/* 消息内容样式 */
.el-message__content {
  font-size: 14px;
  line-height: 1.4;
  font-weight: 400;
  letter-spacing: 0.01em;
}

/* 关闭按钮样式 */
.el-message__closeBtn {
  color: rgba(107, 114, 128, 0.7) !important;
  font-size: 14px !important;
  font-weight: normal !important;
  transition: all 0.2s ease;
  padding: 2px !important;
  border-radius: 3px;
  opacity: 0.7;
}

.el-message__closeBtn:hover {
  color: rgba(107, 114, 128, 1) !important;
  background-color: rgba(107, 114, 128, 0.1);
  opacity: 1;
  transform: none;
}

/* 暗色模式下的 ElMessage 样式 */
body.dark .el-message {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25), 0 1px 3px rgba(0, 0, 0, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.05) !important;
}

/* 暗色模式下的成功消息 */
body.dark .el-message--success {
  background: rgba(6, 78, 59, 0.9) !important;
  color: #a7f3d0 !important;
  border-left-color: #10b981 !important;
  border-color: rgba(16, 185, 129, 0.3) !important;
}

/* 暗色模式下的错误消息 */
body.dark .el-message--error {
  background: rgba(127, 29, 29, 0.9) !important;
  color: #fecaca !important;
  border-left-color: #ef4444 !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

/* 暗色模式下的警告消息 */
body.dark .el-message--warning {
  background: rgba(146, 64, 14, 0.9) !important;
  color: #fed7aa !important;
  border-left-color: #f59e0b !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
}

/* 暗色模式下的信息消息 */
body.dark .el-message--info {
  background: rgba(30, 58, 138, 0.9) !important;
  color: #bfdbfe !important;
  border-left-color: #3b82f6 !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

/* 暗色模式下的关闭按钮 */
body.dark .el-message__closeBtn {
  color: rgba(156, 163, 175, 0.7) !important;
}

body.dark .el-message__closeBtn:hover {
  color: rgba(156, 163, 175, 1) !important;
  background-color: rgba(156, 163, 175, 0.1);
}

/* 消息动画效果 - 从下往上的平滑动画 */
.el-message.el-message-fade-enter-active {
  animation: el-message-slide-up 0.5s cubic-bezier(0.16, 1, 0.3, 1);
}

.el-message.el-message-fade-leave-active {
  animation: el-message-slide-down 0.4s cubic-bezier(0.7, 0, 0.84, 0);
}

/* 从下往上滑入动画 - 保持居中 */
@keyframes el-message-slide-up {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(40px) scale(0.95);
    filter: blur(2px);
  }
  30% {
    opacity: 0.6;
    transform: translateX(-50%) translateY(15px) scale(0.98);
    filter: blur(1px);
  }
  70% {
    opacity: 0.9;
    transform: translateX(-50%) translateY(-2px) scale(1.01);
    filter: blur(0.3px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
    filter: blur(0);
  }
}

/* 向下滑出动画 - 保持居中 */
@keyframes el-message-slide-down {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
    filter: blur(0);
  }
  30% {
    opacity: 0.8;
    transform: translateX(-50%) translateY(8px) scale(0.99);
    filter: blur(0.5px);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(30px) scale(0.95);
    filter: blur(1px);
  }
}



/* 移动端适配 */
@media (max-width: 768px) {
  .el-message {
    min-width: 280px;
    max-width: calc(100vw - 32px);
    font-size: 13px;
    padding: 14px 16px !important;

    /* 移动端强制居中 */
    left: 50% !important;
    transform: translateX(-50%) !important;
    margin: 0 !important;
  }

  .el-message__icon {
    font-size: 16px;
    margin-right: 10px !important;
  }

  .el-message__content {
    font-size: 13px;
  }

  .el-message__closeBtn {
    font-size: 14px !important;
  }
}

/* 强制覆盖 Element Plus 的默认定位样式 */
.el-message-container {
  position: fixed !important;
  top: 16px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 3001 !important;
  pointer-events: none;
}

.el-message-container .el-message {
  pointer-events: auto;
  margin-bottom: 16px;
}

/* 确保消息容器在所有情况下都居中 */
.el-message[style*="left"],
.el-message[style*="right"],
.el-message[style*="margin"] {
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}
